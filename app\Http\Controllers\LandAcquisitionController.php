<?php

namespace App\Http\Controllers;

use App\Models\LandAcquisition;
use App\Models\LandOwner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LandAcquisitionController extends Controller
{
    /**
     * Display a listing of land acquisitions
     */
    public function index(Request $request): JsonResponse
    {
        $query = LandAcquisition::with('landOwner');

        // Apply filters
        if ($request->has('mauza') && $request->mauza !== '') {
            $query->byMauza($request->mauza);
        }

        if ($request->has('khatian') && $request->khatian !== '') {
            $query->byKhatian($request->khatian);
        }

        if ($request->has('record_dag') && $request->record_dag !== '') {
            $query->byRecordDag($request->record_dag);
        }

        if ($request->has('min_price') && $request->has('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }

        if ($request->has('min_land_size') && $request->has('max_land_size')) {
            $query->byLandSizeRange($request->min_land_size, $request->max_land_size);
        }

        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('record_dag', 'like', "%{$search}%")
                  ->orWhere('khatian', 'like', "%{$search}%")
                  ->orWhere('mauza', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $landAcquisitions = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $landAcquisitions,
            'statistics' => LandAcquisition::getStatistics()
        ]);
    }

    /**
     * Store a newly created land acquisition
     */
    public function store(Request $request): JsonResponse
    {
        // Validate land acquisition fields
        $validated = $request->validate([
            'record_dag' => 'required|string|max:255|unique:land_acquisitions,record_dag',
            'khatian' => 'required|string|max:255',
            'mauza' => 'required|string|max:255',
            'land_size' => 'required|numeric|min:0',
            'acquisition_price' => 'required|numeric|min:0',

            // For existing land owner
            'landOwners_id' => 'nullable|integer|exists:land_owners,id',

            // For new land owner (conditional validation will be done below)
            'owner_name' => 'nullable|string|max:255',
            'father_name' => 'nullable|string|max:255',
            'mother_name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Accept file upload
            'ownership_percentage' => 'nullable|numeric|min:0|max:100',
            'cash_received' => 'nullable|numeric|min:0',
        ]);

        try {
            // Start database transaction
            DB::beginTransaction();

            $landOwnerId = null;

            // Check if using existing land owner or creating new one
            $landOwnersId = $request->input('landOwners_id');
            
            if (!empty($landOwnersId) && $landOwnersId !== '' && $landOwnersId !== null) {
                // Use existing land owner
                $landOwnerId = $landOwnersId;
            } else {
                // Create new land owner - validate required fields
                if (empty($validated['owner_name']) || empty($validated['father_name']) || empty($validated['address'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Owner name, father name, and address are required for new land owner'
                    ], 422);
                }

                // Handle photo upload if present
                $photoUrl = null;
                if ($request->hasFile('photo')) {
                    try {
                        $photoUrl = $this->handleImageUpload($request->file('photo'));
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json([
                            'success' => false,
                            'message' => 'Photo upload failed: ' . $e->getMessage()
                        ], 500);
                    }
                }

                $landOwner = LandOwner::create([
                    'name' => $validated['owner_name'],
                    'father_name' => $validated['father_name'],
                    'mother_name' => $validated['mother_name'],
                    'phone' => $validated['phone'],
                    'email' => $validated['email'],
                    'address' => $validated['address'],
                    'photo' => $photoUrl,
                    'ownership_percentage' => $validated['ownership_percentage'],
                    'cash_received' => $validated['cash_received'],
                ]);

                $landOwnerId = $landOwner->id;
            }

            // Create land acquisition
            $landAcquisition = LandAcquisition::create([
                'record_dag' => $validated['record_dag'],
                'khatian' => $validated['khatian'],
                'mauza' => $validated['mauza'],
                'land_size' => $validated['land_size'],
                'acquisition_price' => $validated['acquisition_price'],
                'landOwners_id' => $landOwnerId,
            ]);

            // Load the relationship
            $landAcquisition->load('landOwner');

            // Commit the transaction
            DB::commit();

            $message = $landOwnersId
                ? 'Land acquisition created with existing owner successfully'
                : 'Land acquisition and new owner created successfully';

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $landAcquisition
            ], 201);

        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error creating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified land acquisition
     */
    public function show(LandAcquisition $landAcquisition): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $landAcquisition->load('landOwner')
        ]);
    }

    /**
     * Update the specified land acquisition
     */
    public function update(Request $request, LandAcquisition $landAcquisition): JsonResponse
    {
        // Validate both land acquisition and land owner data
        $validated = $request->validate([
            // Land acquisition fields
            'record_dag' => 'sometimes|required|string|max:255|unique:land_acquisitions,record_dag,' . $landAcquisition->id,
            'khatian' => 'sometimes|required|string|max:255',
            'mauza' => 'sometimes|required|string|max:255',
            'land_size' => 'sometimes|required|numeric|min:0',
            'acquisition_price' => 'sometimes|required|numeric|min:0',

            // Land owner fields
            'owner_name' => 'sometimes|required|string|max:255',
            'father_name' => 'sometimes|required|string|max:255',
            'mother_name' => 'sometimes|nullable|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'email' => 'sometimes|nullable|email|max:255',
            'address' => 'sometimes|required|string',
            'photo' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Accept file upload
            'ownership_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'cash_received' => 'sometimes|nullable|numeric|min:0',
        ]);

        try {
            // Start database transaction
            DB::beginTransaction();

            // Update land owner if land owner fields are provided
            if (isset($validated['owner_name']) || isset($validated['father_name']) || isset($validated['address'])) {
                $landOwner = $landAcquisition->landOwner;
                if ($landOwner) {
                    $landOwnerData = [];
                    if (isset($validated['owner_name'])) $landOwnerData['name'] = $validated['owner_name'];
                    if (isset($validated['father_name'])) $landOwnerData['father_name'] = $validated['father_name'];
                    if (isset($validated['mother_name'])) $landOwnerData['mother_name'] = $validated['mother_name'];
                    if (isset($validated['phone'])) $landOwnerData['phone'] = $validated['phone'];
                    if (isset($validated['email'])) $landOwnerData['email'] = $validated['email'];
                    if (isset($validated['address'])) $landOwnerData['address'] = $validated['address'];
                    if (isset($validated['ownership_percentage'])) $landOwnerData['ownership_percentage'] = $validated['ownership_percentage'];
                    if (isset($validated['cash_received'])) $landOwnerData['cash_received'] = $validated['cash_received'];

                    // Handle photo upload if present
                    if ($request->hasFile('photo')) {
                        // Delete old photo if exists
                        if ($landOwner->photo) {
                            $this->deleteOldImage($landOwner->photo);
                        }
                        
                        try {
                            $photoUrl = $this->handleImageUpload($request->file('photo'));
                            $landOwnerData['photo'] = $photoUrl;
                        } catch (\Exception $e) {
                            DB::rollback();
                            return response()->json([
                                'success' => false,
                                'message' => 'Photo upload failed: ' . $e->getMessage()
                            ], 500);
                        }
                    }

                    $landOwner->update($landOwnerData);
                }
            }

            // Update land acquisition
            $landAcquisitionData = [];
            if (isset($validated['record_dag'])) $landAcquisitionData['record_dag'] = $validated['record_dag'];
            if (isset($validated['khatian'])) $landAcquisitionData['khatian'] = $validated['khatian'];
            if (isset($validated['mauza'])) $landAcquisitionData['mauza'] = $validated['mauza'];
            if (isset($validated['land_size'])) $landAcquisitionData['land_size'] = $validated['land_size'];
            if (isset($validated['acquisition_price'])) $landAcquisitionData['acquisition_price'] = $validated['acquisition_price'];

            $landAcquisition->update($landAcquisitionData);

            // Commit the transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Land acquisition and owner updated successfully',
                'data' => $landAcquisition->load('landOwner')
            ]);

        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error updating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land acquisition
     */
    public function destroy(LandAcquisition $landAcquisition): JsonResponse
    {
        $landAcquisition->delete();

        return response()->json([
            'success' => true,
            'message' => 'Land acquisition deleted successfully'
        ]);
    }

    /**
     * Get dashboard statistics
     */
    public function statistics(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => LandAcquisition::getStatistics()
        ]);
    }

    /**
     * Handle image upload for land owner photos
     */
    private function handleImageUpload($file): string
    {
        try {
            \Log::info('🔄 Starting image upload process (Land Acquisition)', [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'temp_path' => $file->getPathname(),
                'is_valid' => $file->isValid()
            ]);

            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded file is not valid');
            }

            // Generate unique filename
            $filename = 'landowner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            \Log::info('📁 Generated filename', ['filename' => $filename]);
            
            // Create public/landowners directory if it doesn't exist
            $publicDir = public_path('landowners/photo');
            if (!is_dir($publicDir)) {
                \Log::info('📁 Creating public directory', ['dir' => $publicDir]);
                mkdir($publicDir, 0755, true);
            }
            
            // Move the uploaded file to public/landowners directory
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                \Log::info('📁 File moved to public directory', [
                    'destination' => $destinationPath,
                    'public_path' => '/landowners/photo/' . $filename
                ]);
                
                // Return the public URL path
                $url = '/landowners/photo/' . $filename;
                
                // Verify the file exists
                if (file_exists($destinationPath)) {
                    $fileSize = filesize($destinationPath);
                    \Log::info('✅ Upload verification passed', [
                        'file_path' => $destinationPath,
                        'public_url' => $url,
                        'file_size' => $fileSize
                    ]);
                } else {
                    \Log::error('❌ Upload verification failed - file not found');
                    throw new \Exception('File was not properly stored');
                }
                
                return $url;
            } else {
                throw new \Exception('Failed to move uploaded file to public directory');
            }
            
        } catch (\Exception $e) {
            \Log::error('💥 Image upload completely failed (Land Acquisition)', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_info' => [
                    'name' => $file->getClientOriginalName() ?? 'unknown',
                    'size' => $file->getSize() ?? 0,
                    'error_code' => $file->getError() ?? 'none'
                ]
            ]);
            throw $e;
        }
    }

    /**
     * Delete old image file
     */
    private function deleteOldImage(string $imageUrl): void
    {
        try {
            // Extract the filename from URL
            // URL format: /landowners/photo/filename.jpg
            $filename = basename($imageUrl);
            
            // Construct the full file path with proper directory separators
            $filePath = public_path('landowners' . DIRECTORY_SEPARATOR . 'photo' . DIRECTORY_SEPARATOR . $filename);
            
            \Log::info('Attempting to delete image file (Land Acquisition)', [
                'image_url' => $imageUrl,
                'filename' => $filename,
                'file_path' => $filePath,
                'file_exists' => file_exists($filePath)
            ]);
            
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    \Log::info('Old image deleted successfully (Land Acquisition)', ['file_path' => $filePath]);
                } else {
                    \Log::warning('Failed to delete image file (Land Acquisition)', ['file_path' => $filePath]);
                }
            } else {
                \Log::info('Old image file not found, skipping deletion (Land Acquisition)', ['file_path' => $filePath]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to delete old image (Land Acquisition)', [
                'image_url' => $imageUrl,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
