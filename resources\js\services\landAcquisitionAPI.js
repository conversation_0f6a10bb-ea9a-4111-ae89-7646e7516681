import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Remove invalid token and reload page
      localStorage.removeItem('auth_token');
      window.location.reload();
    }
    return Promise.reject(error);
  }
);

// Land Acquisition API functions
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-acquisitions', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land acquisitions:', error);
    throw error;
  }
};

// Get single land acquisition by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-acquisitions/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching land acquisition ${id}:`, error);
    throw error;
  }
};

// Create new land acquisition
export const create = async (data) => {
  try {
    // Check if we have file data that needs FormData
    const hasFile = data.photo && typeof data.photo === 'object' && data.photo instanceof File;
    
    let requestData;
    let headers = {};
    
    if (hasFile) {
      // Use FormData for file uploads
      requestData = new FormData();
      
      // Append all form fields to FormData - include landOwners_id even if empty
      Object.keys(data).forEach(key => {
        if (data[key] !== null && data[key] !== undefined) {
          // Always include landOwners_id field even if empty string
          if (key === 'landOwners_id' || data[key] !== '') {
            requestData.append(key, data[key]);
          }
        }
      });
      
      headers['Content-Type'] = 'multipart/form-data';
    } else {
      // Use regular JSON for non-file data
      requestData = data;
      headers['Content-Type'] = 'application/json';
    }
    
    const response = await api.post('/land-acquisitions', requestData, { headers });
    return response.data;
  } catch (error) {
    console.error('Error creating land acquisition:', error);
    throw error;
  }
};

// Update existing land acquisition
export const update = async (id, data) => {
  try {
    // Check if we have file data that needs FormData
    const hasFile = data.photo && typeof data.photo === 'object' && data.photo instanceof File;
    
    console.log('🔄 Land Acquisition Update Request:', {
      id,
      hasFile,
      photoType: typeof data.photo,
      photoValue: data.photo
    });
    
    let requestData;
    let headers = {};
    
    if (hasFile) {
      console.log('📁 Using FormData for file upload');
      // Use FormData for file uploads
      requestData = new FormData();
      
      // Add _method for Laravel PUT request simulation
      requestData.append('_method', 'PUT');
      
      // Append all form fields to FormData
      Object.keys(data).forEach(key => {
        if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
          requestData.append(key, data[key]);
        }
      });
      
      // Don't set Content-Type header - let browser set it with proper boundary
      const response = await api.post(`/land-acquisitions/${id}`, requestData);
      return response.data;
    } else {
      console.log('📄 Using JSON for regular update');
      // Use regular JSON for non-file data
      requestData = data;
      headers['Content-Type'] = 'application/json';
      
      const response = await api.put(`/land-acquisitions/${id}`, requestData, { headers });
      return response.data;
    }
  } catch (error) {
    console.error(`Error updating land acquisition ${id}:`, error);
    throw error;
  }
};

// Delete land acquisition
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-acquisitions/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting land acquisition ${id}:`, error);
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-acquisitions-statistics');
    return response.data;
  } catch (error) {
    console.error('Error fetching land acquisition statistics:', error);
    throw error;
  }
};

// Get land owners dropdown data
export const getLandOwnersDropdown = async () => {
  try {
    const response = await api.get('/land-owners-dropdown');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners dropdown:', error);
    throw error;
  }
};
