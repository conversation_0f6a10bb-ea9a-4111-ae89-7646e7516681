import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Only set Content-Type to application/json for non-FormData requests
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Remove invalid token and reload page
      localStorage.removeItem('auth_token');
      window.location.reload();
    }
    return Promise.reject(error);
  }
);

// Land Owner API functions

// Get all land owners with pagination and search
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-owners', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners:', error);
    throw error;
  }
};

// Get single land owner by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching land owner ${id}:`, error);
    throw error;
  }
};

// Create new land owner
export const create = async (data) => {
  try {
    // Create FormData if data contains a file
    const formData = new FormData();
    
    for (const key in data) {
      if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
        formData.append(key, data[key]);
      }
    }

    // Don't set Content-Type header - let browser set it with proper boundary
    const response = await api.post('/land-owners', formData);
    return response.data;
  } catch (error) {
    console.error('Error creating land owner:', error);
    // Log more details about the error
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }
    throw error;
  }
};

// Update existing land owner
export const update = async (id, data) => {
  try {
    // Check if we have file data that needs FormData
    const hasFile = data.photo && typeof data.photo === 'object' && data.photo instanceof File;
    
    console.log('🔄 Land Owner Update Request:', {
      id,
      hasFile,
      photoType: typeof data.photo,
      photoValue: data.photo
    });
    
    let requestData;
    let headers = {};
    
    if (hasFile) {
      console.log('📁 Using FormData for file upload');
      // Use FormData for file uploads
      requestData = new FormData();
      
      // Add _method for Laravel PUT request simulation
      requestData.append('_method', 'PUT');
      
      // Append all form fields to FormData
      Object.keys(data).forEach(key => {
        if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
          requestData.append(key, data[key]);
        }
      });
      
      // Don't set Content-Type header - let browser set it with proper boundary
      const response = await api.post(`/land-owners/${id}`, requestData);
      return response.data;
    } else {
      console.log('📄 Using JSON for regular update');
      // Use regular JSON for non-file data
      requestData = data;
      headers['Content-Type'] = 'application/json';
      
      const response = await api.put(`/land-owners/${id}`, requestData, { headers });
      return response.data;
    }
  } catch (error) {
    console.error(`Error updating land owner ${id}:`, error);
    // Log more details about the error
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }
    throw error;
  }
};

// Delete land owner
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting land owner ${id}:`, error);
    throw error;
  }
};

// Get land owners for dropdown (if this endpoint exists)
export const getForDropdown = async () => {
  try {
    const response = await api.get('/land-owners/dropdown');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners for dropdown:', error);
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-owners/statistics');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owner statistics:', error);
    throw error;
  }
};


