<!DOCTYPE html>
<html>
<head>
    <title>Image Upload Debug Tool</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 50px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0; 
            border-left: 4px solid;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        .warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border-color: #ffc107;
        }
        button { 
            padding: 12px 24px; 
            margin: 10px 5px; 
            cursor: pointer; 
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { 
            padding: 10px; 
            border: 2px dashed #ccc; 
            border-radius: 5px; 
            width: 100%; 
            box-sizing: border-box;
        }
        .results { margin-top: 20px; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            white-space: pre-wrap;
        }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Image Upload Debug Tool</h1>
        <p>This tool helps debug image upload issues in the Land Owner system.</p>
        
        <div class="status info">
            <strong>Instructions:</strong><br>
            1. Select an image file below<br>
            2. Click "Debug Image Upload" to test the upload process<br>
            3. Check the detailed results for any issues<br>
            4. Monitor the Laravel logs for additional details
        </div>

        <h2>Upload Tests</h2>
        
        <div class="form-group">
            <label for="imageFile">Select Image File:</label>
            <input type="file" id="imageFile" accept="image/*" />
        </div>
        
        <button class="btn-primary" onclick="debugImageUpload()">🔍 Debug Image Upload</button>
        <button class="btn-success" onclick="testRegularUpload()">📤 Test Regular Land Owner Creation</button>
        <button class="btn-danger" onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results" class="results"></div>
    </div>

    <script>
        // Environment-aware API base URL
        function getApiBaseUrl() {
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://localhost:8000/api';
            }
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }
        
        const API_BASE = getApiBaseUrl();
        let authToken = null;

        // Try to get token from localStorage or prompt user
        async function ensureAuthenticated() {
            authToken = localStorage.getItem('auth_token');
            if (!authToken) {
                const token = prompt('Please enter your authentication token (from localStorage):');
                if (token) {
                    authToken = token;
                    localStorage.setItem('auth_token', token);
                } else {
                    throw new Error('Authentication token required');
                }
            }
        }

        async function debugImageUpload() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="status info">🔄 Starting debug upload test...</div>';
            
            try {
                await ensureAuthenticated();
                
                const fileInput = document.getElementById('imageFile');
                const file = fileInput.files[0];
                
                if (!file) {
                    results.innerHTML = '<div class="status error">❌ Please select an image file first</div>';
                    return;
                }
                
                const formData = new FormData();
                formData.append('photo', file);
                
                results.innerHTML += '<div class="status info">📤 Uploading file: ' + file.name + ' (' + file.size + ' bytes)</div>';
                
                const response = await fetch(API_BASE + '/debug-image-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML += '<div class="status success">✅ Debug upload successful!</div>';
                    results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    // Test if image is accessible
                    if (data.upload_result && data.upload_result.photo_url) {
                        results.innerHTML += '<div class="status info">🖼️ Testing image accessibility...</div>';
                        const img = new Image();
                        img.onload = function() {
                            results.innerHTML += '<div class="status success">✅ Image is accessible via web!</div>';
                            results.innerHTML += '<img src="' + API_BASE.replace('/api', '') + data.upload_result.photo_url + '" style="max-width: 200px; border: 1px solid #ccc; margin: 10px 0;" />';
                        };
                        img.onerror = function() {
                            results.innerHTML += '<div class="status error">❌ Image is NOT accessible via web</div>';
                        };
                        img.src = API_BASE.replace('/api', '') + data.upload_result.photo_url;
                    }
                } else {
                    results.innerHTML += '<div class="status error">❌ Debug upload failed: ' + data.message + '</div>';
                    results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
                
            } catch (error) {
                results.innerHTML += '<div class="status error">❌ Debug test failed: ' + error.message + '</div>';
                console.error('Debug upload error:', error);
            }
        }

        async function testRegularUpload() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="status info">🔄 Testing regular land owner creation with image...</div>';
            
            try {
                await ensureAuthenticated();
                
                const fileInput = document.getElementById('imageFile');
                const file = fileInput.files[0];
                
                if (!file) {
                    results.innerHTML = '<div class="status error">❌ Please select an image file first</div>';
                    return;
                }
                
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('name', 'Test Owner');
                formData.append('father_name', 'Test Father');
                formData.append('address', 'Test Address');
                formData.append('phone', '1234567890');
                formData.append('email', '<EMAIL>');
                formData.append('ownership_percentage', '50');
                formData.append('cash_received', '100000');
                
                const response = await fetch(API_BASE + '/land-owners', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML += '<div class="status success">✅ Land owner created successfully!</div>';
                    results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    results.innerHTML += '<div class="status error">❌ Land owner creation failed: ' + data.message + '</div>';
                    results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
                
            } catch (error) {
                results.innerHTML += '<div class="status error">❌ Regular upload test failed: ' + error.message + '</div>';
                console.error('Regular upload error:', error);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
