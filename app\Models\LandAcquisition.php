<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class LandAcquisition extends Model
{
    use HasFactory;

    protected $fillable = [
        'record_dag',
        'khatian',
        'mauza',
        'land_size',
        'acquisition_price',
        'landOwners_id',
    ];

    protected $casts = [
        'land_size' => 'decimal:2',
        'acquisition_price' => 'decimal:2',
        'landOwners_id' => 'integer',
    ];

    /**
     * Get the formatted acquisition price
     */
    protected function formattedAcquisitionPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => '৳' . number_format($this->acquisition_price, 2)
        );
    }

    /**
     * Get the formatted land size
     */
    protected function formattedLandSize(): Attribute
    {
        return Attribute::make(
            get: fn () => number_format($this->land_size, 2) . ' decimal'
        );
    }

    /**
     * Relationship with LandOwner (assuming you have a LandOwner model)
     */
    public function landOwner()
    {
        return $this->belongsTo(LandOwner::class, 'landOwners_id');
    }

    /**
     * Scope for filtering by mauza
     */
    public function scopeByMauza($query, $mauza)
    {
        return $query->where('mauza', 'like', "%{$mauza}%");
    }

    /**
     * Scope for filtering by khatian
     */
    public function scopeByKhatian($query, $khatian)
    {
        return $query->where('khatian', $khatian);
    }

    /**
     * Scope for filtering by record DAG
     */
    public function scopeByRecordDag($query, $recordDag)
    {
        return $query->where('record_dag', $recordDag);
    }

    /**
     * Scope for filtering by price range
     */
    public function scopeByPriceRange($query, $min, $max)
    {
        return $query->whereBetween('acquisition_price', [$min, $max]);
    }

    /**
     * Scope for filtering by land size range
     */
    public function scopeByLandSizeRange($query, $min, $max)
    {
        return $query->whereBetween('land_size', [$min, $max]);
    }

    /**
     * Get total statistics
     */
    public static function getStatistics(): array
    {
        return [
            'total_records' => self::count(),
            'total_land_size' => self::sum('land_size'),
            'total_acquisition_value' => self::sum('acquisition_price'),
            'average_price_per_decimal' => self::count() > 0 ? self::sum('acquisition_price') / self::sum('land_size') : 0,
        ];
    }
}
