<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $adminRole = Role::where('name', 'Admin')->first();
        $managerRole = Role::where('name', 'Manager')->first();
        $editorRole = Role::where('name', 'Editor')->first();
        $viewerRole = Role::where('name', 'Viewer')->first();

        $users = [
            [
                'name' => 'Super Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $superAdminRole?->id,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $adminRole?->id,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Manager User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Editor User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $editorRole?->id,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Viewer User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $viewerRole?->id,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }
    }
}
