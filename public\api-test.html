<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API URL Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>API URL Configuration Test</h1>
    
    <div class="info">
        <h3>Current Environment Detection:</h3>
        <p><strong>Current Host:</strong> <span id="currentHost"></span></p>
        <p><strong>Current Protocol:</strong> <span id="currentProtocol"></span></p>
        <p><strong>Is Development:</strong> <span id="isDev"></span></p>
        <p><strong>API Base URL:</strong> <span id="apiUrl"></span></p>
    </div>

    <div class="info">
        <h3>Test Different Environments:</h3>
        <button onclick="testLocalhost()">Simulate localhost</button>
        <button onclick="testProduction()">Simulate production</button>
        <button onclick="testReal()">Test Current Environment</button>
    </div>

    <div id="results"></div>

    <script>
        // Copy the same function from your API config
        function getApiBaseUrl() {
            if (import.meta?.env?.DEV || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://127.0.0.1:8000/api';
            }
            
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }

        function updateInfo() {
            document.getElementById('currentHost').textContent = window.location.hostname;
            document.getElementById('currentProtocol').textContent = window.location.protocol;
            document.getElementById('isDev').textContent = 
                (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') ? 'YES' : 'NO';
            document.getElementById('apiUrl').textContent = getApiBaseUrl();
        }

        function testLocalhost() {
            const results = document.getElementById('results');
            const testUrl = 'http://127.0.0.1:8000/api'; // Expected for localhost
            results.innerHTML = `
                <div class="info">
                    <h4>Localhost Test:</h4>
                    <p>Expected URL: ${testUrl}</p>
                    <p>This should be used when running on localhost or 127.0.0.1</p>
                </div>
            `;
        }

        function testProduction() {
            const results = document.getElementById('results');
            const currentProtocol = window.location.protocol;
            const currentHost = window.location.host;
            const expectedUrl = `${currentProtocol}//${currentHost}/api`;
            
            results.innerHTML = `
                <div class="info">
                    <h4>Production Test:</h4>
                    <p>Expected URL: ${expectedUrl}</p>
                    <p>This should be used when running on production domain</p>
                </div>
            `;
        }

        async function testReal() {
            const results = document.getElementById('results');
            const apiUrl = getApiBaseUrl();
            
            results.innerHTML = '<div class="info">Testing actual API connection...</div>';
            
            try {
                // Test if the API is accessible
                const response = await fetch(apiUrl + '/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ email: 'test', password: 'test' })
                });
                
                results.innerHTML = `
                    <div class="success">
                        <h4>API Connection Test Results:</h4>
                        <p><strong>API URL:</strong> ${apiUrl}</p>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong> API is accessible (even if credentials are wrong, this is expected)</p>
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `
                    <div class="error">
                        <h4>API Connection Test Results:</h4>
                        <p><strong>API URL:</strong> ${apiUrl}</p>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>This might be expected if the backend is not running or CORS is not configured.</p>
                    </div>
                `;
            }
        }

        // Initialize on page load
        updateInfo();
    </script>
</body>
</html>
