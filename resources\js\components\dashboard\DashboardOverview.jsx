import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import VisitorsChart from '@/components/ui/visitors-chart';
import ProjectTable from '@/components/ui/project-table';
import {
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const DashboardOverview = () => {

  const stats = [
    {
      title: "Total Revenue",
      value: "$45,231.00",
      change: "+20.1%",
      trend: "up",
      icon: DollarSign,
      description: "from last month"
    },
    {
      title: "Subscriptions",
      value: "+2350",
      change: "+180.1%",
      trend: "up",
      icon: Users,
      description: "from last month"
    },
    {
      title: "Sales",
      value: "+12,234",
      change: "+19%",
      trend: "up",
      icon: ShoppingCart,
      description: "from last month"
    },
    {
      title: "Active Now",
      value: "+573",
      change: "+201",
      trend: "up",
      icon: Activity,
      description: "from last hour"
    }
  ];

  
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Here's what's happening with your business today.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">Download</Button>
          <Button>View Report</Button>
        </div>
      </div>

      {/* Stats grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const isPositive = stat.trend === 'up';
          const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;

          return (
            <Card key={index} className="p-6">
              <CardContent className="p-0">
                {/* Header with title and trend indicator */}
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <div className="flex items-center text-xs">
                    <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
                    <span className={`font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>

                {/* Main value */}
                <div className="text-3xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>

                {/* Description with trend icon */}
                <div className="flex items-center text-sm text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
                  <span>{stat.description}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts and recent activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Total Visitors Chart */}
        <VisitorsChart />

     

       
      </div>

      {/* Project Table Section */}
      <div className="mt-6">
        <ProjectTable />
      </div>
    </div>
  );
};

export default DashboardOverview;
