import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as landOwnerAPI from '../../services/landOwnerAPI';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import SimpleImageUpload from '@/components/ui/SimpleImageUpload';
import ImageModal from '@/components/ui/ImageModal';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Phone,
  Mail,
  MapPin,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

const LandOwnersPage = () => {
  // State management
  const [landOwners, setLandOwners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingOwner, setEditingOwner] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);

  const [formData, setFormData] = useState({
    name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    email: '',
    photo: '',
    ownership_percentage: '',
    cash_received: ''
  });

  const [editFormData, setEditFormData] = useState({
    name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    email: '',
    photo: '',
    ownership_percentage: '',
    cash_received: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Image modal state
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState({ url: '', ownerName: '', title: '' });

  // Sweet Alert helper
  const showAlert = {
    success: (title, text) => {
      Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        confirmButtonColor: '#10b981',
      });
    },
    error: (title, text) => {
      Swal.fire({
        icon: 'error',
        title: title,
        text: text,
        confirmButtonColor: '#ef4444',
      });
    },
    warning: (title, text) => {
      Swal.fire({
        icon: 'warning',
        title: title,
        text: text,
        confirmButtonColor: '#f59e0b',
      });
    },
    loading: (title, text) => {
      Swal.fire({
        title: title,
        text: text,
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });
    }
  };

  // API functions
  const fetchLandOwners = async (search = '', page = 1, limit = 10) => {
    try {
      setLoading(true);
      const params = {
        page: page.toString(),
        per_page: limit.toString(),
        ...(search && { search })
      };

      const result = await landOwnerAPI.getAll(params);

      if (result.success) {
        setLandOwners(result.data.data || []);
        setCurrentPage(result.data.current_page || 1);
        setTotalPages(result.data.last_page || 1);
        setTotalRecords(result.data.total || 0);
      }
    } catch (error) {
      console.error('Error fetching land owners:', error);
      showAlert.error('Error!', 'Failed to fetch land owners. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, prepend the storage URL
    return `${window.location.protocol}//${window.location.host}/storage/landowners/${photoPath}`;
  };

  // Handle opening image modal
  const handleImageClick = (imageUrl, ownerName) => {
    const fullImageUrl = getImageUrl(imageUrl);
    if (fullImageUrl) {
      setSelectedImage({
        url: fullImageUrl,
        ownerName: ownerName,
        title: `${ownerName}'s Photo`
      });
      setShowImageModal(true);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!formData.name || !formData.father_name || !formData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    // Optional: Validate ownership percentage if provided
    if (formData.ownership_percentage && (formData.ownership_percentage < 0 || formData.ownership_percentage > 100)) {
      showAlert.warning('Validation Error!', 'Ownership percentage must be between 0 and 100.');
      return;
    }

    // Optional: Validate cash received if provided
    if (formData.cash_received && formData.cash_received < 0) {
      showAlert.warning('Validation Error!', 'Cash received cannot be negative.');
      return;
    }

    setIsSubmitting(true);

    // Show loading alert
    showAlert.loading(
      'Creating Land Owner...',
      'Please wait while we create the land owner record.'
    );

    try {
      const result = await landOwnerAPI.create(formData);

      if (result.success) {
        // Reset form
        setFormData({
          name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          email: '',
          photo: '',
          ownership_percentage: '',
          cash_received: ''
        });

        setShowAddForm(false);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner created successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('Error creating land owner:', error);
      showAlert.error(
        'Error!',
        'Error creating land owner. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchLandOwners(searchTerm, 1, perPage);
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchLandOwners(searchTerm, newPage, perPage);
    }
  };

  const handleEditOwner = (owner) => {
    setEditingOwner(owner);
    setEditFormData({
      name: owner.name || '',
      father_name: owner.father_name || '',
      mother_name: owner.mother_name || '',
      address: owner.address || '',
      phone: owner.phone || '',
      email: owner.email || '',
      photo: owner.photo || '',
      ownership_percentage: owner.ownership_percentage || '',
      cash_received: owner.cash_received || ''
    });
    setShowEditForm(true);
  };

  const handleDeleteOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete ${owner.name}? This action cannot be undone!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!'
    });

    if (result.isConfirmed) {
      try {
        const deleteResult = await landOwnerAPI.delete_(owner.id);
        console.log('Delete result:', deleteResult);

        if (deleteResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Deleted!',
            'Land owner has been deleted successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            deleteResult.message || 'Failed to delete land owner.'
          );
        }
      } catch (error) {
        console.error('Error deleting land owner:', error);
        showAlert.error(
          'Error!',
          'Error deleting land owner. Please try again.'
        );
      }
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!editFormData.name || !editFormData.father_name || !editFormData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (editFormData.email && !/\S+@\S+\.\S+/.test(editFormData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    // Optional: Validate ownership percentage if provided
    if (editFormData.ownership_percentage && (editFormData.ownership_percentage < 0 || editFormData.ownership_percentage > 100)) {
      showAlert.warning('Validation Error!', 'Ownership percentage must be between 0 and 100.');
      return;
    }

    // Optional: Validate cash received if provided
    if (editFormData.cash_received && editFormData.cash_received < 0) {
      showAlert.warning('Validation Error!', 'Cash received cannot be negative.');
      return;
    }

    setIsUpdating(true);

    // Show loading alert
    showAlert.loading(
      'Updating Land Owner...',
      'Please wait while we update the land owner record.'
    );

    try {
      // Debug logging
      console.log('📝 Edit form data before submission:', editFormData);
      console.log('📷 Photo data type:', typeof editFormData.photo);
      console.log('📷 Photo instanceof File:', editFormData.photo instanceof File);
      
      const result = await landOwnerAPI.update(editingOwner.id, editFormData);
      console.log('✅ Update API Response:', result);

      if (result.success) {
        // Reset form
        setEditFormData({
          name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          email: '',
          photo: '',
          ownership_percentage: '',
          cash_received: ''
        });

        setShowEditForm(false);
        setEditingOwner(null);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner updated successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('Error updating land owner:', error);
      showAlert.error(
        'Error!',
        'Error updating land owner. Please try again.'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchLandOwners('', 1, perPage);
  }, [perPage]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Land Owners</h1>
          <p className="text-muted-foreground">
            Manage land owner records and information
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Owner
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Land Owners
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by name, father's name, phone, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Land Owners Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Land Owners ({totalRecords})
          </CardTitle>
          <CardDescription>
            A list of all land owners in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : landOwners.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No land owners found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating a new land owner.'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>S.N</TableHead>
                    <TableHead>Photo</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Father's Name</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Address</TableHead>
                    <TableHead>Ownership %</TableHead>
                    <TableHead>Cash Received</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {landOwners.map((owner, index) => (
                    <TableRow key={owner.id}>
                     <TableCell className="text-center text-muted-foreground">
                        {((currentPage - 1) * perPage) + index + 1}
                      </TableCell>
                      <TableCell>
                        {owner.photo ? (
                          <img
                            src={owner.photo}
                            alt={owner.name}
                            style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', cursor: 'pointer' }}
                            onClick={() => handleImageClick(owner.photo, owner.name)}
                          />
                        ) : (
                          <div 
                            style={{ 
                              width: '60px', 
                              height: '60px', 
                              backgroundColor: '#f3f4f6', 
                              borderRadius: '8px', 
                              display: 'flex', 
                              alignItems: 'center', 
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6b7280'
                            }}
                          >
                            No Image
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{owner.name}</TableCell>
                      <TableCell>{owner.father_name}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {owner.phone && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              {owner.phone}
                            </div>
                          )}
                          {owner.email && (
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3" />
                              {owner.email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate max-w-[200px]">{owner.address}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {owner.ownership_percentage ? (
                          <Badge variant="secondary">{owner.ownership_percentage}%</Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {owner.cash_received ? (
                          <span className="font-medium">৳{Number(owner.cash_received).toLocaleString()}</span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditOwner(owner)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteOwner(owner)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add New Owner Modal */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Add New Land Owner</DialogTitle>
            <DialogDescription>
              Enter the land owner information.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Abdul Rahman"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="father_name">Father's Name *</Label>
                  <Input
                    id="father_name"
                    value={formData.father_name}
                    onChange={(e) => handleInputChange('father_name', e.target.value)}
                    placeholder="Abdul Karim"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="mother_name">Mother's Name</Label>
                <Input
                  id="mother_name"
                  value={formData.mother_name}
                  onChange={(e) => handleInputChange('mother_name', e.target.value)}
                  placeholder="Fatima Begum"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+880171234567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ownership_percentage">Ownership Percentage (%)</Label>
                  <Input
                    id="ownership_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.ownership_percentage}
                    onChange={(e) => handleInputChange('ownership_percentage', e.target.value)}
                    placeholder="100.00"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cash_received">Cash Received (৳)</Label>
                  <Input
                    id="cash_received"
                    type="number"
                    value={formData.cash_received}
                    onChange={(e) => handleInputChange('cash_received', e.target.value)}
                    placeholder="2500000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <SimpleImageUpload
                  value={formData.photo}
                  onChange={(file) => handleInputChange('photo', file)}
                  label="Owner Photo (Optional)"
                  placeholder="Upload owner's photo (not required)"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-gray-500">Image upload is optional. You can add it later if needed.</p>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Creating...' : 'Create Owner'}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Owner Modal */}
      <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Edit Land Owner</DialogTitle>
            <DialogDescription>
              Update the land owner information.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleUpdateSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_name">Name *</Label>
                  <Input
                    id="edit_name"
                    value={editFormData.name}
                    onChange={(e) => handleEditInputChange('name', e.target.value)}
                    placeholder="Abdul Rahman"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_father_name">Father's Name *</Label>
                  <Input
                    id="edit_father_name"
                    value={editFormData.father_name}
                    onChange={(e) => handleEditInputChange('father_name', e.target.value)}
                    placeholder="Abdul Karim"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_mother_name">Mother's Name</Label>
                <Input
                  id="edit_mother_name"
                  value={editFormData.mother_name}
                  onChange={(e) => handleEditInputChange('mother_name', e.target.value)}
                  placeholder="Fatima Begum"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_address">Address *</Label>
                <Input
                  id="edit_address"
                  value={editFormData.address}
                  onChange={(e) => handleEditInputChange('address', e.target.value)}
                  placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_phone">Phone</Label>
                  <Input
                    id="edit_phone"
                    value={editFormData.phone}
                    onChange={(e) => handleEditInputChange('phone', e.target.value)}
                    placeholder="+880171234567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_email">Email</Label>
                  <Input
                    id="edit_email"
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => handleEditInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_ownership_percentage">Ownership Percentage (%)</Label>
                  <Input
                    id="edit_ownership_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={editFormData.ownership_percentage}
                    onChange={(e) => handleEditInputChange('ownership_percentage', e.target.value)}
                    placeholder="100.00"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_cash_received">Cash Received (৳)</Label>
                  <Input
                    id="edit_cash_received"
                    type="number"
                    value={editFormData.cash_received}
                    onChange={(e) => handleEditInputChange('cash_received', e.target.value)}
                    placeholder="2500000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <SimpleImageUpload
                  value={editFormData.photo}
                  onChange={(file) => handleEditInputChange('photo', file)}
                  label="Owner Photo (Optional)"
                  placeholder="Upload owner's photo (not required)"
                  disabled={isUpdating}
                />
                <p className="text-xs text-gray-500">Image upload is optional. You can update it later if needed.</p>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowEditForm(false)}
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? 'Updating...' : 'Update Owner'}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Modal */}
      <ImageModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        imageUrl={selectedImage.url}
        title={selectedImage.title}
        ownerName={selectedImage.ownerName}
      />
    </div>
  );
};

export default LandOwnersPage;
