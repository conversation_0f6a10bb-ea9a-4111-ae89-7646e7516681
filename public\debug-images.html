<!DOCTYPE html>
<html>
<head>
    <title>Image URL Debug Tool</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 50px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0; 
            border-left: 4px solid;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        button { 
            padding: 12px 24px; 
            margin: 10px 5px; 
            cursor: pointer; 
            border: none;
            border-radius: 5px;
            font-size: 16px;
            background-color: #007bff; 
            color: white;
        }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            white-space: pre-wrap;
        }
        .image-test {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .image-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .image-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image URL Debug Tool</h1>
        <p>This tool helps debug image URLs and display issues in the Land Owner system.</p>
        
        <button onclick="testLandOwnerImages()">🔍 Test Land Owner Images</button>
        <button onclick="testImageUrls()">🌐 Test Image URL Formats</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Environment-aware API base URL
        function getApiBaseUrl() {
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://localhost:8000/api';
            }
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }
        
        const API_BASE = getApiBaseUrl();
        let authToken = null;

        // Try to get token from localStorage or prompt user
        async function ensureAuthenticated() {
            authToken = localStorage.getItem('auth_token');
            if (!authToken) {
                const token = prompt('Please enter your authentication token (from localStorage):');
                if (token) {
                    authToken = token;
                    localStorage.setItem('auth_token', token);
                } else {
                    throw new Error('Authentication token required');
                }
            }
        }

        // Helper function to get full image URL
        function getImageUrl(photoPath) {
            if (!photoPath) return null;
            
            // If it's already a full URL, return as is
            if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
                return photoPath;
            }
            
            // If it starts with /storage, use the base URL
            if (photoPath.startsWith('/storage')) {
                return `${window.location.protocol}//${window.location.host}${photoPath}`;
            }
            
            // If it's just a filename or relative path, prepend the storage URL
            return `${window.location.protocol}//${window.location.host}/storage/landowners/${photoPath}`;
        }

        async function testLandOwnerImages() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="status info">🔄 Fetching land owners...</div>';
            
            try {
                await ensureAuthenticated();
                
                const response = await fetch(API_BASE + '/land-owners', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + authToken,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success && data.data.data) {
                    const landOwners = data.data.data;
                    const ownersWithPhotos = landOwners.filter(owner => owner.photo);
                    
                    results.innerHTML += `<div class="status success">✅ Found ${landOwners.length} land owners, ${ownersWithPhotos.length} with photos</div>`;
                    
                    if (ownersWithPhotos.length > 0) {
                        results.innerHTML += '<h3>Land Owners with Photos:</h3>';
                        results.innerHTML += '<div class="image-grid">';
                        
                        ownersWithPhotos.forEach(owner => {
                            const fullUrl = getImageUrl(owner.photo);
                            results.innerHTML += `
                                <div class="image-item">
                                    <div><strong>${owner.name}</strong></div>
                                    <div style="font-size: 12px; color: #666; margin: 5px 0;">
                                        Original: ${owner.photo}<br>
                                        Full URL: ${fullUrl}
                                    </div>
                                    <img 
                                        src="${fullUrl}" 
                                        alt="${owner.name}'s photo"
                                        onload="this.style.border='2px solid green'"
                                        onerror="this.style.border='2px solid red'; this.alt='❌ Failed to load'"
                                        title="Original: ${owner.photo}"
                                    />
                                    <div style="font-size: 10px; margin-top: 5px;">
                                        <button onclick="testSingleImage('${fullUrl}', '${owner.name}')">Test URL</button>
                                    </div>
                                </div>
                            `;
                        });
                        
                        results.innerHTML += '</div>';
                    } else {
                        results.innerHTML += '<div class="status info">ℹ️ No land owners have photos uploaded yet</div>';
                    }
                } else {
                    results.innerHTML += '<div class="status error">❌ Failed to fetch land owners: ' + (data.message || 'Unknown error') + '</div>';
                }
                
            } catch (error) {
                results.innerHTML += '<div class="status error">❌ Error: ' + error.message + '</div>';
                console.error('Error fetching land owners:', error);
            }
        }

        async function testSingleImage(imageUrl, ownerName) {
            try {
                const response = await fetch(imageUrl, { method: 'HEAD' });
                alert(`Image test for ${ownerName}:\nURL: ${imageUrl}\nStatus: ${response.status} ${response.statusText}\nAccessible: ${response.ok ? 'Yes' : 'No'}`);
            } catch (error) {
                alert(`Image test for ${ownerName}:\nURL: ${imageUrl}\nError: ${error.message}\nAccessible: No`);
            }
        }

        async function testImageUrls() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="status info">🔄 Testing different image URL formats...</div>';

            const testUrls = [
                '/storage/landowners/test.jpg',
                'storage/landowners/test.jpg',
                getApiBaseUrl().replace('/api', '') + '/storage/landowners/test.jpg',
                'landowner_123_abc.jpg'
            ];

            testUrls.forEach(testUrl => {
                const fullUrl = getImageUrl(testUrl);
                results.innerHTML += `
                    <div class="image-test">
                        <strong>Input:</strong> ${testUrl}<br>
                        <strong>Output:</strong> ${fullUrl}<br>
                        <button onclick="testSingleImage('${fullUrl}', 'Test')">Test This URL</button>
                    </div>
                `;
            });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
