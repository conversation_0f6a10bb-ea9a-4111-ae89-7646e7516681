<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LandAcquisitionController;
use App\Http\Controllers\LandOwnerController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AuthController;

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::get('permissions', [AuthController::class, 'permissions']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    });
});

// Protected API routes - require authentication
Route::middleware('auth:sanctum')->group(function () {
    
    // Land Acquisition routes - with permission checks
    Route::middleware('permission:land-acquisition,read')->group(function () {
        Route::get('land-acquisitions', [LandAcquisitionController::class, 'index']);
        Route::get('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'show']);
        Route::get('land-acquisitions-statistics', [LandAcquisitionController::class, 'statistics']);
    });
    
    Route::middleware('permission:land-acquisition,create')->group(function () {
        Route::post('land-acquisitions', [LandAcquisitionController::class, 'store']);
    });
    
    Route::middleware('permission:land-acquisition,update')->group(function () {
        Route::put('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::patch('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::post('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']); // For file uploads with _method=PUT
    });
    
    Route::middleware('permission:land-acquisition,delete')->group(function () {
        Route::delete('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'destroy']);
    });

    // Land Owner routes - with permission checks
    Route::middleware('permission:land-owners,read')->group(function () {
        Route::get('land-owners', [LandOwnerController::class, 'index']);
        Route::get('land-owners/{landOwner}', [LandOwnerController::class, 'show']);
        Route::get('land-owners-dropdown', [LandOwnerController::class, 'dropdown']);
    });
    
    Route::middleware('permission:land-owners,create')->group(function () {
        Route::post('land-owners', [LandOwnerController::class, 'store']);
    });
    
    Route::middleware('permission:land-owners,update')->group(function () {
        Route::put('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
        Route::patch('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
        Route::post('land-owners/{landOwner}', [LandOwnerController::class, 'update']); // For file uploads with _method=PUT
    });
    
    Route::middleware('permission:land-owners,delete')->group(function () {
        Route::delete('land-owners/{landOwner}', [LandOwnerController::class, 'destroy']);
    });

    // Role Management routes - with permission checks
    Route::middleware('permission:role,read')->group(function () {
        Route::get('roles', [RoleController::class, 'index']);
        Route::get('roles/{role}', [RoleController::class, 'show']);
        Route::get('roles-statistics', [RoleController::class, 'getStatistics']);
        Route::get('roles-modules', [RoleController::class, 'getAvailableModules']);
    });
    
    Route::middleware('permission:role,create')->group(function () {
        Route::post('roles', [RoleController::class, 'store']);
    });
    
    Route::middleware('permission:role,update')->group(function () {
        Route::put('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles-bulk-status', [RoleController::class, 'bulkUpdateStatus']);
    });
    
    Route::middleware('permission:role,delete')->group(function () {
        Route::delete('roles/{role}', [RoleController::class, 'destroy']);
    });
});

// Legacy route for compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
