<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Land Owner Update Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a8b; }
        .status { padding: 15px; border-radius: 5px; margin: 15px 0; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Land Owner Update Test</h1>
        <p>This tool helps test land owner updates with and without images.</p>

        <!-- Land Owner Selection -->
        <div class="test-section">
            <h3>1. Select Land Owner to Update</h3>
            <button onclick="fetchLandOwners()">📄 Load Land Owners</button>
            <div id="landOwnersSelect" style="margin-top: 10px;"></div>
        </div>

        <!-- Update Form -->
        <div class="test-section">
            <h3>2. Update Form</h3>
            <form id="updateForm">
                <div class="form-group">
                    <label for="name">Name *</label>
                    <input type="text" id="name" required>
                </div>
                
                <div class="form-group">
                    <label for="father_name">Father's Name *</label>
                    <input type="text" id="father_name" required>
                </div>
                
                <div class="form-group">
                    <label for="address">Address *</label>
                    <textarea id="address" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone</label>
                    <input type="text" id="phone">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email">
                </div>
                
                <div class="form-group">
                    <label for="photo">Photo (Test File Upload)</label>
                    <input type="file" id="photo" accept="image/*">
                    <div id="currentPhoto"></div>
                </div>

                <button type="button" onclick="testUpdateWithoutImage()">📝 Test Update WITHOUT Image</button>
                <button type="button" onclick="testUpdateWithImage()">📷 Test Update WITH Image</button>
                <button type="button" onclick="testUpdateRemoveImage()">🗑️ Test Update REMOVE Image</button>
            </form>
        </div>

        <!-- Results -->
        <div class="test-section">
            <h3>3. Test Results</h3>
            <button onclick="clearResults()">🗑️ Clear Results</button>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // Environment-aware API base URL
        function getApiBaseUrl() {
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return 'http://127.0.0.1:8000/api';
            }
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }
        
        const API_BASE = getApiBaseUrl();
        let authToken = null;
        let selectedLandOwner = null;

        // Authentication
        async function ensureAuthenticated() {
            authToken = localStorage.getItem('auth_token');
            if (!authToken) {
                const token = prompt('Please enter your authentication token (from localStorage):');
                if (token) {
                    authToken = token;
                    localStorage.setItem('auth_token', token);
                } else {
                    throw new Error('Authentication token required');
                }
            }
        }

        // Fetch land owners
        async function fetchLandOwners() {
            const results = document.getElementById('results');
            const selectDiv = document.getElementById('landOwnersSelect');
            
            try {
                await ensureAuthenticated();
                
                results.innerHTML = '<div class="info">🔄 Fetching land owners...</div>';
                
                const response = await fetch(`${API_BASE}/land-owners?per_page=20`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success && data.data && data.data.data) {
                    const landOwners = data.data.data;
                    
                    let selectHtml = '<select id="landOwnerSelect" onchange="selectLandOwner()">';
                    selectHtml += '<option value="">-- Select a Land Owner --</option>';
                    
                    landOwners.forEach(owner => {
                        selectHtml += `<option value="${owner.id}" data-owner='${JSON.stringify(owner)}'>${owner.name} (${owner.father_name})</option>`;
                    });
                    
                    selectHtml += '</select>';
                    selectDiv.innerHTML = selectHtml;
                    
                    results.innerHTML = `<div class="success">✅ Found ${landOwners.length} land owners</div>`;
                } else {
                    results.innerHTML = '<div class="error">❌ Failed to fetch land owners</div>';
                }
                
            } catch (error) {
                results.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        // Select land owner
        function selectLandOwner() {
            const select = document.getElementById('landOwnerSelect');
            const option = select.selectedOptions[0];
            
            if (option.value) {
                selectedLandOwner = JSON.parse(option.dataset.owner);
                
                // Populate form
                document.getElementById('name').value = selectedLandOwner.name || '';
                document.getElementById('father_name').value = selectedLandOwner.father_name || '';
                document.getElementById('address').value = selectedLandOwner.address || '';
                document.getElementById('phone').value = selectedLandOwner.phone || '';
                document.getElementById('email').value = selectedLandOwner.email || '';
                
                // Show current photo
                const currentPhotoDiv = document.getElementById('currentPhoto');
                if (selectedLandOwner.photo) {
                    const photoUrl = selectedLandOwner.photo.startsWith('http') ? 
                        selectedLandOwner.photo : 
                        `${window.location.protocol}//${window.location.host}${selectedLandOwner.photo}`;
                    currentPhotoDiv.innerHTML = `
                        <p>Current Photo:</p>
                        <img src="${photoUrl}" style="max-width: 200px; border: 1px solid #ccc; margin: 10px 0;" alt="Current photo" />
                        <p><small>${selectedLandOwner.photo}</small></p>
                    `;
                } else {
                    currentPhotoDiv.innerHTML = '<p>No current photo</p>';
                }
                
                document.getElementById('results').innerHTML = `<div class="info">📝 Selected: ${selectedLandOwner.name}</div>`;
            }
        }

        // Test update without image
        async function testUpdateWithoutImage() {
            if (!selectedLandOwner) {
                alert('Please select a land owner first');
                return;
            }

            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">🔄 Testing update without image...</div>';

            try {
                const updateData = {
                    name: document.getElementById('name').value,
                    father_name: document.getElementById('father_name').value,
                    address: document.getElementById('address').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value
                    // Note: NOT including photo field
                };

                console.log('📝 Update data (no image):', updateData);

                const response = await fetch(`${API_BASE}/land-owners/${selectedLandOwner.id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.success) {
                    results.innerHTML = '<div class="success">✅ Update without image successful!</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    results.innerHTML = '<div class="error">❌ Update failed</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }

            } catch (error) {
                results.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Update error:', error);
            }
        }

        // Test update with image
        async function testUpdateWithImage() {
            if (!selectedLandOwner) {
                alert('Please select a land owner first');
                return;
            }

            const photoInput = document.getElementById('photo');
            if (!photoInput.files[0]) {
                alert('Please select an image file first');
                return;
            }

            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">🔄 Testing update with image...</div>';

            try {
                const formData = new FormData();
                formData.append('_method', 'PUT');
                formData.append('name', document.getElementById('name').value);
                formData.append('father_name', document.getElementById('father_name').value);
                formData.append('address', document.getElementById('address').value);
                formData.append('phone', document.getElementById('phone').value);
                formData.append('email', document.getElementById('email').value);
                formData.append('photo', photoInput.files[0]);

                console.log('📁 FormData contents:');
                for (let [key, value] of formData.entries()) {
                    console.log(`${key}:`, value);
                }

                const response = await fetch(`${API_BASE}/land-owners/${selectedLandOwner.id}`, {
                    method: 'POST', // Using POST with _method=PUT for file upload
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                        // Note: NOT setting Content-Type, let browser set it with boundary
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    results.innerHTML = '<div class="success">✅ Update with image successful!</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    
                    // Update the current photo display
                    if (result.data && result.data.photo) {
                        const currentPhotoDiv = document.getElementById('currentPhoto');
                        const photoUrl = result.data.photo.startsWith('http') ? 
                            result.data.photo : 
                            `${window.location.protocol}//${window.location.host}${result.data.photo}`;
                        currentPhotoDiv.innerHTML = `
                            <p>Updated Photo:</p>
                            <img src="${photoUrl}" style="max-width: 200px; border: 1px solid #ccc; margin: 10px 0;" alt="Updated photo" />
                            <p><small>${result.data.photo}</small></p>
                        `;
                    }
                } else {
                    results.innerHTML = '<div class="error">❌ Update with image failed</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }

            } catch (error) {
                results.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Update error:', error);
            }
        }

        // Test update remove image
        async function testUpdateRemoveImage() {
            if (!selectedLandOwner) {
                alert('Please select a land owner first');
                return;
            }

            const results = document.getElementById('results');
            results.innerHTML = '<div class="info">🔄 Testing update remove image...</div>';

            try {
                const updateData = {
                    name: document.getElementById('name').value,
                    father_name: document.getElementById('father_name').value,
                    address: document.getElementById('address').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    photo: '' // Empty string to remove photo
                };

                console.log('🗑️ Update data (remove image):', updateData);

                const response = await fetch(`${API_BASE}/land-owners/${selectedLandOwner.id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.success) {
                    results.innerHTML = '<div class="success">✅ Remove image successful!</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    
                    // Update the current photo display
                    const currentPhotoDiv = document.getElementById('currentPhoto');
                    currentPhotoDiv.innerHTML = '<p>Photo removed successfully</p>';
                } else {
                    results.innerHTML = '<div class="error">❌ Remove image failed</div>';
                    results.innerHTML += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }

            } catch (error) {
                results.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Update error:', error);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
