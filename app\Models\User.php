<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user's role
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Check if user has access to a specific module
     */
    public function hasModuleAccess(string $moduleKey): bool
    {
        if (!$this->role) {
            return false;
        }

        return in_array($moduleKey, $this->role->accessible_modules ?? []);
    }

    /**
     * Check if user has a specific permission for a module
     */
    public function hasPermission(string $moduleKey, string $permission): bool
    {
        if (!$this->role) {
            return false;
        }

        $modulePermissions = $this->role->module_permissions[$moduleKey] ?? [];
        return in_array($permission, $modulePermissions);
    }

    /**
     * Get user's accessible modules
     */
    public function getAccessibleModulesAttribute(): array
    {
        return $this->role ? ($this->role->accessible_modules ?? []) : [];
    }

    /**
     * Get user's permissions
     */
    public function getPermissionsAttribute(): array
    {
        return $this->role ? ($this->role->module_permissions ?? []) : [];
    }
}
