// Centralized API configuration
export const getApiBaseUrl = () => {
  // Check if we're in development mode
  if (import.meta.env.DEV || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://127.0.0.1:8000/api';
  }
  
  // For production, use the same protocol and host as the current page
  const protocol = window.location.protocol;
  const host = window.location.host;
  return `${protocol}//${host}/api`;
};

export const API_BASE_URL = getApiBaseUrl();
