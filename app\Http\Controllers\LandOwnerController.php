<?php

namespace App\Http\Controllers;

use App\Models\LandOwner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LandOwnerController extends Controller
{
  
    public function index(Request $request): JsonResponse
    {
        $query = LandOwner::query();

        // Apply search filter
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('father_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('nid_number', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 50);
        $landOwners = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $landOwners
        ]);
    }

    /**
     * Store a newly created land owner
     */
    public function store(Request $request): JsonResponse
    {
       
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'father_name' => 'required|string|max:255',
                'mother_name' => 'nullable|string|max:255',
                'address' => 'required|string',
                'phone' => 'nullable|string|max:20',
                'nid_number' => 'nullable|string|max:20|unique:land_owners,nid_number',
                'email' => 'nullable|email|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Accept file upload
                'ownership_percentage' => 'nullable|numeric|min:0|max:100',
                'cash_received' => 'nullable|numeric|min:0',
            ]);

            // Handle photo upload if present
            $photoUrl = null;
            if ($request->hasFile('photo')) {
                
                
                try {
                    $photoUrl = $this->handleImageUpload($request->file('photo'));
                   
                } catch (\Exception $e) {
                  
                    throw $e;
                }
              
            } 

            // Remove photo from validated data and add photoUrl
            unset($validated['photo']);
            $validated['photo'] = $photoUrl;
            
           

            $landOwner = LandOwner::create($validated);

          

            return response()->json([
                'success' => true,
                'message' => 'Land owner created successfully',
                'data' => $landOwner
            ], 201);

       
    }

    /**
     * Display the specified land owner
     */
    public function show(LandOwner $landOwner): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $landOwner->load('landAcquisitions')
        ]);
    }

    /**
     * Update the specified land owner
     */
    public function update(Request $request, LandOwner $landOwner): JsonResponse
    {
        try {
            \Log::info('🔄 Land Owner Update Request', [
                'id' => $landOwner->id,
                'method' => $request->method(),
                'has_file' => $request->hasFile('photo'),
                'content_type' => $request->header('Content-Type'),
                '_method' => $request->input('_method'),
                'all_inputs' => $request->except(['photo']) // Don't log file data
            ]);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'father_name' => 'sometimes|required|string|max:255',
                'mother_name' => 'sometimes|nullable|string|max:255',
                'address' => 'sometimes|required|string',
                'phone' => 'sometimes|nullable|string|max:20',
                'nid_number' => 'sometimes|nullable|string|max:20|unique:land_owners,nid_number,' . $landOwner->id,
                'email' => 'sometimes|nullable|email|max:255',
                'photo' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Accept file upload
                'ownership_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
                'cash_received' => 'sometimes|nullable|numeric|min:0',
            ]);

            // Handle photo upload if present
            if ($request->hasFile('photo')) {
                \Log::info('📁 Processing photo upload for land owner update', [
                    'landowner_id' => $landOwner->id,
                    'old_photo' => $landOwner->photo,
                    'file_info' => [
                        'name' => $request->file('photo')->getClientOriginalName(),
                        'size' => $request->file('photo')->getSize(),
                        'mime' => $request->file('photo')->getMimeType()
                    ]
                ]);

                // Delete old photo if exists
                if ($landOwner->photo) {
                    \Log::info('🗑️ Deleting old photo', ['old_photo' => $landOwner->photo]);
                    $this->deleteOldImage($landOwner->photo);
                }
                
                // Upload new photo
                $photoUrl = $this->handleImageUpload($request->file('photo'));
                \Log::info('✅ New photo uploaded', ['new_photo_url' => $photoUrl]);
                $validated['photo'] = $photoUrl;
            } else {
                \Log::info('ℹ️ No photo file in request');
            }

            $landOwner->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Land owner updated successfully',
                'data' => $landOwner->fresh()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \Log::error('Land Owner update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'landowner_id' => $landOwner->id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land owner
     */
    public function destroy($id): JsonResponse
    {
        try {
            $owner = LandOwner::find($id);
            if (!$owner) {
                return response()->json(['success' => false, 'message' => 'Not found'], 404);
            }

            // Delete the associated image file if it exists
            if ($owner->photo) {
                $this->deleteOldImage($owner->photo);
            }

            // Delete the database record
            $owner->delete();

            return response()->json(['success' => true, 'message' => 'Land owner and associated files deleted successfully']);

        } catch (\Exception $e) {
            \Log::error('Land Owner deletion failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'landowner_id' => $id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all land owners for dropdown (simplified response)
     */
    public function dropdown(): JsonResponse
    {
        $landOwners = LandOwner::select('id', 'name', 'father_name')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $landOwners
        ]);
    }

    /**
     * Handle image upload for land owner photos
     */
    private function handleImageUpload($file): string
    {
        try {
            \Log::info('🔄 Starting image upload process', [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'temp_path' => $file->getPathname(),
                'is_valid' => $file->isValid()
            ]);

            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded file is not valid');
            }

            // Generate unique filename
            $filename = 'landowner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            \Log::info('📁 Generated filename', ['filename' => $filename]);
            
            // Create public/landowners directory if it doesn't exist
            $publicDir = public_path('landowners/photo');
            if (!is_dir($publicDir)) {
                \Log::info('� Creating public directory', ['dir' => $publicDir]);
                mkdir($publicDir, 0755, true);
            }
            
            // Move the uploaded file to public/landowners directory
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                \Log::info('� File moved to public directory', [
                    'destination' => $destinationPath,
                    'public_path' => '/landowners/photo' . $filename
                ]);
                
                // Return the public URL path
                $url = '/landowners/photo/' . $filename;
                
                // Verify the file exists
                if (file_exists($destinationPath)) {
                    $fileSize = filesize($destinationPath);
                    \Log::info('✅ Upload verification passed', [
                        'file_path' => $destinationPath,
                        'public_url' => $url,
                        'file_size' => $fileSize
                    ]);
                } else {
                    \Log::error('❌ Upload verification failed - file not found');
                    throw new \Exception('File was not properly stored');
                }
                
                return $url;
            } else {
                throw new \Exception('Failed to move uploaded file to public directory');
            }
            
        } catch (\Exception $e) {
            \Log::error('💥 Image upload completely failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_info' => [
                    'name' => $file->getClientOriginalName() ?? 'unknown',
                    'size' => $file->getSize() ?? 0,
                    'error_code' => $file->getError() ?? 'none'
                ]
            ]);
            throw $e;
        }
    }

    /**
     * Delete old image file
     */
    private function deleteOldImage(string $imageUrl): void
    {
        try {
            // Extract the filename from URL
            // URL format: /landowners/photo/filename.jpg
            $filename = basename($imageUrl);
            
            // Construct the full file path with proper directory separators
            $filePath = public_path('landowners' . DIRECTORY_SEPARATOR . 'photo' . DIRECTORY_SEPARATOR . $filename);
            
            \Log::info('Attempting to delete image file', [
                'image_url' => $imageUrl,
                'filename' => $filename,
                'file_path' => $filePath,
                'file_exists' => file_exists($filePath)
            ]);
            
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    \Log::info('Old image deleted successfully', ['file_path' => $filePath]);
                } else {
                    \Log::warning('Failed to delete image file', ['file_path' => $filePath]);
                }
            } else {
                \Log::info('Old image file not found, skipping deletion', ['file_path' => $filePath]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to delete old image', [
                'image_url' => $imageUrl,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
