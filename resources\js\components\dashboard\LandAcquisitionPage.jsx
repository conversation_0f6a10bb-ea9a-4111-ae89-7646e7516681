import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as landAcquisitionAPI from '../../services/landAcquisitionAPI';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import SimpleImageUpload from '@/components/ui/SimpleImageUpload';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  MapPin,
  FileText,
  DollarSign,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';

// SweetAlert2 configuration
const showAlert = {
  success: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#10b981',
      customClass: {
        popup: 'rounded-lg',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  error: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ef4444',
      customClass: {
        popup: 'rounded-lg',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  warning: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#f59e0b',
      customClass: {
        popup: 'rounded-lg',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  confirm: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, Create',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      customClass: {
        popup: 'rounded-lg',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  loading: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'info',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      customClass: {
        popup: 'rounded-lg',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      },
      didOpen: () => {
        Swal.showLoading();
      }
    });
  }
};

const LandAcquisitionPage = () => {
  const [landAcquisitions, setLandAcquisitions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statistics, setStatistics] = useState({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [landOwners, setLandOwners] = useState([]);
  const [isNewLandOwner, setIsNewLandOwner] = useState(false);

  const [formData, setFormData] = useState({
    record_dag: '',
    khatian: '',
    mauza: '',
    land_size: '',
    acquisition_price: '',
    // For existing land owner selection
    landOwners_id: '',
    // Land owner fields (for new land owner)
    owner_name: '',
    father_name: '',
    mother_name: '',
    phone: '',
    email: '',
    address: '',
    photo: '',
    ownership_percentage: '',
    cash_received: ''
  });
  const [editFormData, setEditFormData] = useState({
    record_dag: '',
    khatian: '',
    mauza: '',
    land_size: '',
    acquisition_price: '',
    // Land owner fields for editing
    owner_name: '',
    father_name: '',
    mother_name: '',
    phone: '',
    email: '',
    address: '',
    photo: '',
    ownership_percentage: '',
    cash_received: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // API functions
  const fetchLandAcquisitions = async (search = '', page = 1, itemsPerPage = 10) => {
    try {
      setLoading(true);

      const params = { page, per_page: itemsPerPage };
      if (search.trim()) {
        params.search = search.trim();
      }

      const result = await landAcquisitionAPI.getAll(params);

      if (result.success) {
        setLandAcquisitions(result.data.data || []); // Laravel pagination returns data.data
        setStatistics(result.statistics || {});

        // Set pagination data
        setCurrentPage(result.data.current_page || 1);
        setTotalPages(result.data.last_page || 1);
        setTotalRecords(result.data.total || 0);
      } else {
        console.error('API returned error:', result);
      }
    } catch (error) {
      console.error('Error fetching land acquisitions:', error);
      showAlert.error('Error!', 'Failed to fetch land acquisitions. Please try again.');
      // Fallback to empty data on error
      setLandAcquisitions([]);
      setStatistics({});
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const result = await landAcquisitionAPI.getStatistics();

      if (result.success) {
        setStatistics(result.data || {});
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };



  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!formData.record_dag || !formData.khatian || !formData.mauza ||
        !formData.land_size || !formData.acquisition_price) {
      showAlert.warning('Validation Error!', 'Please fill in all required land acquisition fields.');
      return;
    }

    // Validate land owner data based on checkbox
    if (isNewLandOwner) {
      // Validate new land owner fields
      if (!formData.owner_name || !formData.father_name || !formData.address) {
        showAlert.warning('Validation Error!', 'Please fill in all required land owner fields.');
        return;
      }
      // Clear landOwners_id when creating new land owner
      formData.landOwners_id = '';
    } else {
      // Validate existing land owner selection
      if (!formData.landOwners_id) {
        showAlert.warning('Validation Error!', 'Please select a land owner.');
        return;
      }
      // Ensure landOwners_id is not empty string but a valid number
      if (formData.landOwners_id === '' || formData.landOwners_id === null) {
        showAlert.warning('Validation Error!', 'Please select a valid land owner.');
        return;
      }
    }

    setIsSubmitting(true);

    // Show loading alert
    showAlert.loading(
      'Creating Record...',
      'Please wait while we create the land acquisition record.'
    );

    try {
      const result = await landAcquisitionAPI.create(formData);

      if (result.success) {
        // Reset form
        setFormData({
          record_dag: '',
          khatian: '',
          mauza: '',
          land_size: '',
          acquisition_price: '',
          landOwners_id: '',
          owner_name: '',
          father_name: '',
          mother_name: '',
          phone: '',
          email: '',
          address: '',
          photo: '',
          ownership_percentage: '',
          cash_received: ''
        });

        // Reset checkbox
        setIsNewLandOwner(false);

        // Close modal
        setShowAddForm(false);

        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Success!',
          'Land acquisition record created successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('❌ Error creating land acquisition:', error);
      console.error('❌ Error response:', error.response?.data);
      
      // Network/Server error alert
      showAlert.error(
        'Error!',
        error.response?.data?.message || 'Error creating record. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchLandOwners = async () => {
    try {
      const result = await landAcquisitionAPI.getLandOwnersDropdown();

      if (result.success) {
        setLandOwners(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching land owners:', error);
      setLandOwners([]);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditRecord = (record) => {
    setEditingRecord(record);
    setEditFormData({
      record_dag: record.record_dag,
      khatian: record.khatian,
      mauza: record.mauza,
      land_size: record.land_size,
      acquisition_price: record.acquisition_price,
      // Populate land owner fields from the relationship
      owner_name: record.land_owner?.name || '',
      father_name: record.land_owner?.father_name || '',
      mother_name: record.land_owner?.mother_name || '',
      phone: record.land_owner?.phone || '',
      email: record.land_owner?.email || '',
      address: record.land_owner?.address || '',
      photo: record.land_owner?.photo || '',
      ownership_percentage: record.land_owner?.ownership_percentage || '',
      cash_received: record.land_owner?.cash_received || ''
    });
    setShowEditForm(true);
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!editFormData.record_dag || !editFormData.khatian || !editFormData.mauza ||
        !editFormData.land_size || !editFormData.acquisition_price ||
        !editFormData.owner_name || !editFormData.father_name || !editFormData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields.');
      return;
    }

    setIsUpdating(true);

    // Show loading alert
    showAlert.loading(
      'Updating Record...',
      'Please wait while we update the land acquisition record.'
    );

    try {
      // Debug logging
      console.log('📝 Edit form data before submission:', editFormData);
      console.log('📷 Photo data type:', typeof editFormData.photo);
      console.log('📷 Photo instanceof File:', editFormData.photo instanceof File);
      
      const result = await landAcquisitionAPI.update(editingRecord.id, editFormData);
      console.log('✅ Update API Response:', result);

      if (result.success) {
        // Reset form
        setEditFormData({
          record_dag: '',
          khatian: '',
          mauza: '',
          land_size: '',
          acquisition_price: '',
          owner_name: '',
          father_name: '',
          mother_name: '',
          phone: '',
          email: '',
          address: '',
          photo: '',
          ownership_percentage: '',
          cash_received: ''
        });

        // Close modal
        setShowEditForm(false);
        setEditingRecord(null);

        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Success!',
          'Land acquisition record updated successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('❌ Error updating land acquisition:', error);
      console.error('❌ Error response:', error.response?.data);
      
      // Network/Server error alert
      showAlert.error(
        'Error!',
        error.response?.data?.message || 'Error updating record. Please try again.'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteRecord = async (record) => {
    // Show confirmation dialog
    const result = await showAlert.confirm(
      'Confirm Deletion',
      `Are you sure you want to delete the record "${record.record_dag}"? This action cannot be undone.`
    );

    if (!result.isConfirmed) {
      return; // User cancelled
    }

    // Show loading alert
    showAlert.loading(
      'Deleting Record...',
      'Please wait while we delete the land acquisition record.'
    );

    try {
      const result = await landAcquisitionAPI.delete_(record.id);

      if (result.success) {
        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Deleted!',
          'Land acquisition record has been deleted successfully.'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Failed to delete the record'
        );
      }
    } catch (error) {
      console.error('Error deleting land acquisition:', error);
      // Network/Server error alert
      showAlert.error(
        'Error!',
        'Error deleting record. Please try again.'
      );
    }
  };

  useEffect(() => {
    fetchLandAcquisitions('', 1, perPage);
    fetchStatistics();
    fetchLandOwners();
  }, [perPage]);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
      fetchLandAcquisitions(searchTerm, 1, perPage);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, perPage]);

  // Handle page changes
  useEffect(() => {
    if (currentPage > 1) {
      fetchLandAcquisitions(searchTerm, currentPage, perPage);
    }
  }, [currentPage]);

  // Effect to handle clearing form data when switching between modes
  useEffect(() => {
    if (isNewLandOwner) {
      // Clear existing land owner selection when creating new
      setFormData(prev => ({
        ...prev,
        landOwners_id: ''
      }));
    } else {
      // Clear new land owner fields when selecting existing
      setFormData(prev => ({
        ...prev,
        owner_name: '',
        father_name: '',
        mother_name: '',
        phone: '',
        email: '',
        address: '',
        photo: '',
        ownership_percentage: '',
        cash_received: ''
      }));
    }
  }, [isNewLandOwner]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('En-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatLandSize = (size) => {
    return `${size} decimal`;
  };

  // Since we're doing server-side filtering, we don't need client-side filtering
  const filteredData = landAcquisitions;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading land acquisitions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Land Acquisition</h1>
          <p className="text-muted-foreground">
            Manage land acquisition records and ownership details
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Record
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Records</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+12.5%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {statistics.total_records}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Land acquisition records</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Land Size</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+8.2%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {statistics.total_land_size}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Decimal of land acquired</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Value</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+15.3%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {formatCurrency(statistics.total_acquisition_value)}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Total acquisition cost</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Avg. Price/Decimal</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+4.1%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {formatCurrency(statistics.average_price_per_decimal)}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Average price per decimal</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Land Acquisition Records</CardTitle>
          <CardDescription>
            View and manage all land acquisition records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by Record DAG, Khatian, Mauza, or Owner name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>

          {/* Data Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">S.N.</TableHead>
                  <TableHead>Record DAG</TableHead>
                  <TableHead>Khatian</TableHead>
                  <TableHead>Mauza</TableHead>
                  <TableHead>Land Size</TableHead>
                  <TableHead>Acquisition Price</TableHead>
                  <TableHead>Land Owner</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((record, index) => (
                  <TableRow key={record.id}>
                    <TableCell className="text-center text-muted-foreground">
                      {((currentPage - 1) * perPage) + index + 1}
                    </TableCell>
                    <TableCell className="font-medium">
                      <Badge variant="outline">{record.record_dag}</Badge>
                    </TableCell>
                    <TableCell>{record.khatian}</TableCell>
                    <TableCell>{record.mauza}</TableCell>
                    <TableCell>{formatLandSize(record.land_size)}</TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(record.acquisition_price)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {record.land_owner?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          S/O {record.land_owner?.father_name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {record.land_owner?.phone || 'N/A'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(record.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    
                          <DropdownMenuItem onClick={() => handleEditRecord(record)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Record
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteRecord(record)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Record
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          {filteredData.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                </span>
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">Rows per page</span>
                  <select
                    value={perPage}
                    onChange={(e) => {
                      setPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="border rounded px-2 py-1 text-sm bg-background"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>

                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {filteredData.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No records found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add New Record Modal */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Add New Land Acquisition Record</DialogTitle>
            <DialogDescription>
              Enter the land acquisition details and land owner information.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleSubmit} className="space-y-6">
            {/* Land Acquisition Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Land Acquisition Details</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="record_dag">Record DAG *</Label>
                  <Input
                    id="record_dag"
                    value={formData.record_dag}
                    onChange={(e) => handleInputChange('record_dag', e.target.value)}
                    placeholder="DAG-001"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="khatian">Khatian *</Label>
                  <Input
                    id="khatian"
                    value={formData.khatian}
                    onChange={(e) => handleInputChange('khatian', e.target.value)}
                    placeholder="KH-2024-001"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="mauza">Mauza *</Label>
                <Input
                  id="mauza"
                  value={formData.mauza}
                  onChange={(e) => handleInputChange('mauza', e.target.value)}
                  placeholder="Dhanmondi"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="land_size">Land Size (decimal) *</Label>
                  <Input
                    id="land_size"
                    type="number"
                    step="0.01"
                    value={formData.land_size}
                    onChange={(e) => handleInputChange('land_size', e.target.value)}
                    placeholder="5.25"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="acquisition_price">Acquisition Price (৳) *</Label>
                  <Input
                    id="acquisition_price"
                    type="number"
                    value={formData.acquisition_price}
                    onChange={(e) => handleInputChange('acquisition_price', e.target.value)}
                    placeholder="2500000"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Land Owner Selection */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium">Land Owner Information</h3>

              {/* Checkbox for new land owner */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="new_land_owner"
                  checked={isNewLandOwner}
                  onCheckedChange={setIsNewLandOwner}
                />
                <Label htmlFor="new_land_owner">Create New Land Owner</Label>
              </div>

              {!isNewLandOwner ? (
                // Existing Land Owner Dropdown
                <div className="space-y-2">
                  <Label htmlFor="landOwners_id">Select Land Owner *</Label>
                  <Select
                    value={formData.landOwners_id}
                    onValueChange={(value) => handleInputChange('landOwners_id', value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an existing land owner" />
                    </SelectTrigger>
                    <SelectContent>
                      {landOwners.map((owner) => (
                        <SelectItem key={owner.id} value={owner.id.toString()}>
                          {owner.name} - {owner.father_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                // New Land Owner Form
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="owner_name">Owner Name *</Label>
                      <Input
                        id="owner_name"
                        value={formData.owner_name}
                        onChange={(e) => handleInputChange('owner_name', e.target.value)}
                        placeholder="Abdul Rahman"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="father_name">Father's Name *</Label>
                      <Input
                        id="father_name"
                        value={formData.father_name}
                        onChange={(e) => handleInputChange('father_name', e.target.value)}
                        placeholder="Abdul Karim"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="mother_name">Mother's Name</Label>
                    <Input
                      id="mother_name"
                      value={formData.mother_name}
                      onChange={(e) => handleInputChange('mother_name', e.target.value)}
                      placeholder="Fatima Begum"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address *</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+880171234567"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ownership_percentage">Ownership Percentage (%)</Label>
                      <Input
                        id="ownership_percentage"
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={formData.ownership_percentage}
                        onChange={(e) => handleInputChange('ownership_percentage', e.target.value)}
                        placeholder="100.00"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cash_received">Cash Received (৳)</Label>
                      <Input
                        id="cash_received"
                        type="number"
                        value={formData.cash_received}
                        onChange={(e) => handleInputChange('cash_received', e.target.value)}
                        placeholder="2500000"
                      />
                    </div>
                  </div>

                    <div className="space-y-2">
                      <SimpleImageUpload
                        value={formData.photo}
                        onChange={(file) => handleInputChange('photo', file)}
                        label="Owner Photo (Optional)"
                        placeholder="Upload owner's photo (not required)"
                        disabled={isSubmitting}
                      />
                      <p className="text-xs text-gray-500">Image upload is optional. You can add it later if needed.</p>
                    </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Record'}
              </Button>
            </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Record Modal */}
      <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Edit Land Acquisition Record</DialogTitle>
            <DialogDescription>
              Update the details for the land acquisition record.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleUpdateSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_record_dag">Record DAG</Label>
                <Input
                  id="edit_record_dag"
                  value={editFormData.record_dag}
                  onChange={(e) => handleEditInputChange('record_dag', e.target.value)}
                  placeholder="DAG-001"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_khatian">Khatian</Label>
                <Input
                  id="edit_khatian"
                  value={editFormData.khatian}
                  onChange={(e) => handleEditInputChange('khatian', e.target.value)}
                  placeholder="KH-2024-001"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_mauza">Mauza</Label>
              <Input
                id="edit_mauza"
                value={editFormData.mauza}
                onChange={(e) => handleEditInputChange('mauza', e.target.value)}
                placeholder="Dhanmondi"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_land_size">Land Size (decimal)</Label>
                <Input
                  id="edit_land_size"
                  type="number"
                  step="0.01"
                  value={editFormData.land_size}
                  onChange={(e) => handleEditInputChange('land_size', e.target.value)}
                  placeholder="5.25"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_acquisition_price">Acquisition Price (৳)</Label>
                <Input
                  id="edit_acquisition_price"
                  type="number"
                  value={editFormData.acquisition_price}
                  onChange={(e) => handleEditInputChange('acquisition_price', e.target.value)}
                  placeholder="2500000"
                  required
                />
              </div>
            </div>

            {/* Land Owner Details */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium">Land Owner Information</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_owner_name">Owner Name *</Label>
                  <Input
                    id="edit_owner_name"
                    value={editFormData.owner_name}
                    onChange={(e) => handleEditInputChange('owner_name', e.target.value)}
                    placeholder="Abdul Rahman"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_father_name">Father's Name *</Label>
                  <Input
                    id="edit_father_name"
                    value={editFormData.father_name}
                    onChange={(e) => handleEditInputChange('father_name', e.target.value)}
                    placeholder="Abdul Karim"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_mother_name">Mother's Name</Label>
                <Input
                  id="edit_mother_name"
                  value={editFormData.mother_name}
                  onChange={(e) => handleEditInputChange('mother_name', e.target.value)}
                  placeholder="Fatima Begum"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_address">Address *</Label>
                <Input
                  id="edit_address"
                  value={editFormData.address}
                  onChange={(e) => handleEditInputChange('address', e.target.value)}
                  placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_phone">Phone</Label>
                  <Input
                    id="edit_phone"
                    value={editFormData.phone}
                    onChange={(e) => handleEditInputChange('phone', e.target.value)}
                    placeholder="+880171234567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_email">Email</Label>
                  <Input
                    id="edit_email"
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => handleEditInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_ownership_percentage">Ownership Percentage (%)</Label>
                  <Input
                    id="edit_ownership_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={editFormData.ownership_percentage}
                    onChange={(e) => handleEditInputChange('ownership_percentage', e.target.value)}
                    placeholder="100.00"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_cash_received">Cash Received (৳)</Label>
                  <Input
                    id="edit_cash_received"
                    type="number"
                    value={editFormData.cash_received}
                    onChange={(e) => handleEditInputChange('cash_received', e.target.value)}
                    placeholder="2500000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <SimpleImageUpload
                  value={editFormData.photo}
                  onChange={(file) => handleEditInputChange('photo', file)}
                  label="Owner Photo (Optional)"
                  placeholder="Upload owner's photo (not required)"
                  disabled={isUpdating}
                />
                <p className="text-xs text-gray-500">Image upload is optional. You can update it later if needed.</p>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowEditForm(false);
                  setEditingRecord(null);
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdating}>
                {isUpdating ? 'Updating...' : 'Update Record'}
              </Button>
            </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LandAcquisitionPage;
