# Deployment Guide

This guide explains how to deploy your real estate management application to production while ensuring the API URLs automatically adapt to the environment.

## Changes Made

### 1. Centralized API Configuration
- Created `resources/js/config/api.js` with environment-aware URL detection
- Updated all API service files to use the centralized configuration
- URLs automatically switch between localhost (development) and production domain

### 2. Environment Detection Logic
The application now automatically detects the environment:
- **Development**: Uses `http://127.0.0.1:8000/api` when running on localhost
- **Production**: Uses `https://yourdomain.com/api` (matches the current domain)

### 3. Files Updated
- `resources/js/config/api.js` (new) - Centralized API configuration
- `resources/js/services/authAPI.js` - Updated to use centralized config
- `resources/js/services/roleAPI.js` - Updated to use centralized config
- `resources/js/services/landOwnerAPI.js` - Updated to use centralized config
- `resources/js/services/landAcquisitionAPI.js` - Updated to use centralized config
- `public/debug-upload.html` - Updated for environment-aware URLs
- `public/debug-images.html` - Updated for environment-aware URLs

## Deployment Steps

### 1. Prepare Production Environment File
Copy `.env.production` to `.env` on your production server and update:
```bash
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
DB_HOST=your_production_db_host
DB_DATABASE=your_production_database
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
```

### 2. Build Frontend Assets
```bash
npm install
npm run build
```

### 3. Laravel Setup Commands
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Create storage symlink
php artisan storage:link

# Clear and cache configurations
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 4. Server Configuration
Ensure your web server (Apache/Nginx) points to the `public` directory and has proper URL rewriting configured.

#### Apache (.htaccess in public directory)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

### 5. Set Proper Permissions
```bash
chown -R www-data:www-data /path/to/your/project
chmod -R 755 /path/to/your/project
chmod -R 775 /path/to/your/project/storage
chmod -R 775 /path/to/your/project/bootstrap/cache
```

## API URL Behavior

### Development (localhost)
- Frontend runs on: `http://localhost:3000` or similar
- Backend API: `http://127.0.0.1:8000/api`
- File storage: `http://127.0.0.1:8000/storage`

### Production
- Frontend runs on: `https://yourdomain.com`
- Backend API: `https://yourdomain.com/api`
- File storage: `https://yourdomain.com/storage`

## Environment Variables for Production

Key environment variables to set in production:

```bash
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
APP_KEY=your-32-character-random-string

# Database
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=your-db-name
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password

# Mail (if using email features)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-email-password
```

## Testing the Deployment

1. **API Endpoints**: Test `https://yourdomain.com/api/auth/login`
2. **File Uploads**: Verify file storage works correctly
3. **Frontend**: Ensure React app loads and communicates with API
4. **Authentication**: Test login/logout functionality

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Check `config/cors.php` settings
2. **File Permissions**: Ensure storage and cache directories are writable
3. **Database Connections**: Verify production database credentials
4. **HTTPS**: Ensure SSL certificate is properly configured
5. **Asset Loading**: Run `npm run build` to generate production assets

### Debug Mode:
Temporarily set `APP_DEBUG=true` in production to see detailed error messages, but remember to set it back to `false` for security.

## Security Considerations

1. Set `APP_DEBUG=false` in production
2. Use HTTPS for all production traffic
3. Secure your `.env` file (never commit to version control)
4. Set proper file permissions
5. Use strong database passwords
6. Consider setting up fail2ban for additional security

Your application will now automatically use the correct API URLs based on the environment without any manual configuration changes!
