<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LandOwner extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'father_name',
        'mother_name',
        'address',
        'phone',
        'nid_number',
        'email',
        'photo',
        'ownership_percentage',
        'cash_received',
    ];

    /**
     * Relationship with LandAcquisition
     */
    public function landAcquisitions()
    {
        return $this->hasMany(LandAcquisition::class, 'landOwners_id');
    }

    /**
     * Get full name with father's name
     */
    public function getFullNameAttribute(): string
    {
        return $this->father_name ? "{$this->name} S/O {$this->father_name}" : $this->name;
    }
}
